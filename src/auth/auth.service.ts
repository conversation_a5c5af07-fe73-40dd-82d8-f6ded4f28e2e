import { Injectable } from '@nestjs/common';
import { RegisterDto } from './dto/RegisterDto.dto';
import { LoginDto } from './dto/LoginDto.dto';
import { UserService } from 'src/user/user.service';

@Injectable()
export class AuthService {
  constructor(private readonly userService: UserService) {}

  async register(registerDto: RegisterDto) {
    if(registerDto.password !== registerDto.confirmPassword){
      return {
        code: 400,
        message: '两次输入的密码不一致',
      }
    }
    if(await this.userService.findOneByPhoneNumber(registerDto.phoneNumber)){
      return {
        code: 400,
        message: '该手机号已被注册',
      }
    }
    
    this.userService.create(registerDto);
    return {
      code: 200,
      message: '注册成功',
    }
  }

  async login(loginDto: LoginDto) {
    const user = await this.userService.findOneByPhoneNumber(loginDto.phoneNumber);
    if(!user){
      return {
        code: 400,
        message: '用户不存在',
      }
    }
    console.log('user',user)
    if(user.password === loginDto.password){
      return {
        code: 200,
        message: '登录成功',
      }
    }
  }
}

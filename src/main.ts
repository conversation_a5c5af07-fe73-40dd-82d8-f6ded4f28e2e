import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { MyLogger } from './MyLogger';
import * as mysql from 'mysql2'

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.useLogger(new MyLogger());

  const port = process.env.PORT ?? 3000;
  await app.listen(port);
  console.log(`🚀 应用已启动！`);
  console.log(`📍 服务地址: http://localhost:${port}`);
  console.log(`📍 本地访问: http://127.0.0.1:${port}`);
}
bootstrap();
